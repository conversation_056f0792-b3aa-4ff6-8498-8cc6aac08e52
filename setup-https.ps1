# PowerShell脚本：设置HTTPS证书
Write-Host "🔒 开始设置HTTPS证书..." -ForegroundColor Green

# 检查是否已存在证书
if ((Test-Path "cert.pem") -and (Test-Path "key.pem")) {
    Write-Host "✅ 证书文件已存在，跳过生成" -ForegroundColor Green
} else {
    Write-Host "📝 使用mkcert生成证书..." -ForegroundColor Yellow
    
    # 检查是否安装了mkcert
    try {
        $mkcertVersion = mkcert -version
        Write-Host "✅ 检测到mkcert: $mkcertVersion" -ForegroundColor Green
        
        # 安装本地CA
        mkcert -install
        
        # 生成证书
        mkcert -key-file key.pem -cert-file cert.pem localhost 127.0.0.1 *************
        
        Write-Host "✅ 证书生成完成" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 未找到mkcert，使用备用方案..." -ForegroundColor Red
        Write-Host "📥 请先安装mkcert:" -ForegroundColor Yellow
        Write-Host "   1. 访问: https://github.com/FiloSottile/mkcert/releases" -ForegroundColor Cyan
        Write-Host "   2. 下载Windows版本" -ForegroundColor Cyan
        Write-Host "   3. 或使用Chocolatey: choco install mkcert" -ForegroundColor Cyan
        Write-Host "   4. 或使用Scoop: scoop install mkcert" -ForegroundColor Cyan
        
        # 创建临时证书配置
        Write-Host "🔄 创建临时自签名证书..." -ForegroundColor Yellow
        
        # 使用Node.js生成证书（如果可用）
        $nodeScript = @"
const { execSync } = require('child_process');
const fs = require('fs');

try {
    // 尝试使用openssl
    execSync('openssl version', { stdio: 'ignore' });
    
    // 生成私钥
    execSync('openssl genrsa -out key.pem 2048');
    
    // 生成证书
    execSync('openssl req -new -x509 -key key.pem -out cert.pem -days 365 -subj "/C=CN/ST=Beijing/L=Beijing/O=Interactive Storybook/OU=Development/CN=*************"');
    
    console.log('✅ 使用OpenSSL生成证书成功');
} catch (error) {
    console.log('❌ OpenSSL不可用，请手动配置证书');
    console.log('💡 建议安装mkcert或配置反向代理');
}
"@
        
        $nodeScript | Out-File -FilePath "generate-cert.js" -Encoding UTF8
        
        try {
            node generate-cert.js
            Remove-Item "generate-cert.js" -Force
        }
        catch {
            Write-Host "❌ 无法自动生成证书" -ForegroundColor Red
            Write-Host "💡 请使用以下备用方案之一:" -ForegroundColor Yellow
            Write-Host "   1. 安装mkcert并重新运行此脚本" -ForegroundColor Cyan
            Write-Host "   2. 使用Cloudflare Tunnel" -ForegroundColor Cyan
            Write-Host "   3. 使用ngrok" -ForegroundColor Cyan
        }
    }
}

if ((Test-Path "cert.pem") -and (Test-Path "key.pem")) {
    Write-Host "🚀 证书文件已准备就绪:" -ForegroundColor Green
    Write-Host "  - 私钥: key.pem" -ForegroundColor Cyan
    Write-Host "  - 证书: cert.pem" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "📋 下一步:" -ForegroundColor Yellow
    Write-Host "1. 运行: npm run dev" -ForegroundColor Cyan
    Write-Host "2. 访问: https://*************:5174/" -ForegroundColor Cyan
    Write-Host "3. 在浏览器中接受证书（如果有警告）" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "⚠️  注意:" -ForegroundColor Yellow
    Write-Host "- 如果使用自签名证书，浏览器可能显示警告" -ForegroundColor Red
    Write-Host "- 点击'高级' -> '继续访问'即可" -ForegroundColor Red
    Write-Host "- HTTPS环境下所有功能将正常工作" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "🔄 备用方案:" -ForegroundColor Yellow
    Write-Host "1. 使用ngrok: npm install -g ngrok && ngrok http 5174" -ForegroundColor Cyan
    Write-Host "2. 使用Cloudflare Tunnel (推荐)" -ForegroundColor Cyan
    Write-Host "3. 配置Nginx反向代理" -ForegroundColor Cyan
}
