// 环境诊断工具
// 用于检测浏览器环境和功能支持情况

/**
 * 检查浏览器环境兼容性
 */
export function checkBrowserCompatibility() {
  console.log('🔧 检查浏览器环境兼容性...');
  
  const compatibility = {
    // 基础API支持
    fetch: typeof fetch !== 'undefined',
    textEncoder: typeof TextEncoder !== 'undefined',
    
    // 安全相关
    https: window.location.protocol === 'https:' || window.location.hostname === 'localhost',
    webCrypto: !!(window.crypto && window.crypto.subtle),
    
    // 语音功能
    speechSynthesis: 'speechSynthesis' in window,
    speechRecognition: 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window,
    
    // 媒体设备
    mediaDevices: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    
    // 存储
    localStorage: typeof Storage !== 'undefined' && 'localStorage' in window,
    
    // 浏览器信息
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language
  };
  
  console.log('📊 兼容性检查结果:', compatibility);
  
  return compatibility;
}

/**
 * 检查语音功能支持
 */
export async function checkSpeechSupport() {
  console.log('🎤 检查语音功能支持...');
  
  const speechSupport = {
    synthesis: 'speechSynthesis' in window,
    recognition: 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window,
    mediaDevices: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    microphonePermission: null
  };
  
  // 检查麦克风权限
  if (speechSupport.mediaDevices) {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      speechSupport.microphonePermission = 'granted';
      // 立即停止流以释放麦克风
      stream.getTracks().forEach(track => track.stop());
    } catch (error) {
      if (error.name === 'NotAllowedError') {
        speechSupport.microphonePermission = 'denied';
      } else if (error.name === 'NotFoundError') {
        speechSupport.microphonePermission = 'no-device';
      } else {
        speechSupport.microphonePermission = 'unknown';
      }
    }
  }
  
  console.log('🎤 语音功能支持结果:', speechSupport);
  return speechSupport;
}

/**
 * 检查Web Crypto API支持
 */
export async function checkWebCryptoSupport() {
  console.log('🔐 检查Web Crypto API支持...');
  
  const cryptoSupport = {
    available: !!(window.crypto && window.crypto.subtle),
    canImportKey: false,
    canSign: false,
    error: null
  };
  
  if (cryptoSupport.available) {
    try {
      // 测试密钥导入
      const encoder = new TextEncoder();
      const keyData = encoder.encode('test-key');
      
      const cryptoKey = await window.crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'HMAC', hash: 'SHA-1' },
        false,
        ['sign']
      );
      
      cryptoSupport.canImportKey = true;
      
      // 测试签名
      const contentData = encoder.encode('test-content');
      await window.crypto.subtle.sign('HMAC', cryptoKey, contentData);
      cryptoSupport.canSign = true;
      
    } catch (error) {
      cryptoSupport.error = error.message;
    }
  }
  
  console.log('🔐 Web Crypto API支持结果:', cryptoSupport);
  return cryptoSupport;
}

/**
 * 生成环境诊断报告
 */
export async function generateDiagnosticReport() {
  console.log('🏥 生成环境诊断报告...');
  
  const report = {
    timestamp: new Date().toISOString(),
    browser: checkBrowserCompatibility(),
    speech: await checkSpeechSupport(),
    crypto: await checkWebCryptoSupport(),
    recommendations: []
  };
  
  // 生成建议
  if (!report.browser.https) {
    report.recommendations.push('建议使用HTTPS访问应用以确保所有功能正常工作');
  }
  
  if (!report.browser.webCrypto) {
    report.recommendations.push('Web Crypto API不可用，请使用现代浏览器或确保HTTPS环境');
  }
  
  if (!report.speech.recognition) {
    report.recommendations.push('语音识别不支持，建议使用Chrome、Edge或Safari浏览器');
  }
  
  if (report.speech.microphonePermission === 'denied') {
    report.recommendations.push('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问');
  }
  
  if (!report.crypto.canSign) {
    report.recommendations.push('签名功能不可用，可能影响图片生成功能');
  }
  
  console.log('📋 诊断报告:', report);
  return report;
}

/**
 * 显示用户友好的诊断信息
 */
export function displayDiagnosticInfo(report) {
  console.log('📢 显示诊断信息...');
  
  let message = '🔍 环境检测结果：\n\n';
  
  // 基础功能
  message += '📱 基础功能：\n';
  message += `  ✅ 浏览器兼容性: ${report.browser.fetch ? '支持' : '不支持'}\n`;
  message += `  ${report.browser.https ? '✅' : '⚠️'} HTTPS环境: ${report.browser.https ? '是' : '否'}\n`;
  message += `  ${report.browser.localStorage ? '✅' : '❌'} 本地存储: ${report.browser.localStorage ? '支持' : '不支持'}\n\n`;
  
  // 语音功能
  message += '🎤 语音功能：\n';
  message += `  ${report.speech.synthesis ? '✅' : '❌'} 语音合成: ${report.speech.synthesis ? '支持' : '不支持'}\n`;
  message += `  ${report.speech.recognition ? '✅' : '❌'} 语音识别: ${report.speech.recognition ? '支持' : '不支持'}\n`;
  message += `  ${report.speech.microphonePermission === 'granted' ? '✅' : '⚠️'} 麦克风权限: ${report.speech.microphonePermission || '未知'}\n\n`;
  
  // 图片生成功能
  message += '🎨 图片生成功能：\n';
  message += `  ${report.crypto.available ? '✅' : '❌'} Web Crypto API: ${report.crypto.available ? '可用' : '不可用'}\n`;
  message += `  ${report.crypto.canSign ? '✅' : '❌'} 签名功能: ${report.crypto.canSign ? '正常' : '异常'}\n\n`;
  
  // 建议
  if (report.recommendations.length > 0) {
    message += '💡 建议：\n';
    report.recommendations.forEach((rec, index) => {
      message += `  ${index + 1}. ${rec}\n`;
    });
  } else {
    message += '🎉 所有功能都正常工作！';
  }
  
  return message;
}

/**
 * 快速环境检查（用于应用启动时）
 */
export async function quickEnvironmentCheck() {
  const issues = [];
  
  // 检查关键功能
  if (!window.crypto || !window.crypto.subtle) {
    issues.push('Web Crypto API不可用，图片生成功能可能无法正常工作');
  }
  
  if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
    issues.push('语音识别功能不支持');
  }
  
  if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
    issues.push('非HTTPS环境，某些功能可能受限');
  }
  
  return issues;
}
