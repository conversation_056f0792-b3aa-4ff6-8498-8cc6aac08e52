// 测试修复效果的脚本
// 在浏览器控制台中运行此脚本来验证修复

console.log('🧪 开始测试修复效果...');

// 测试1: 检查环境兼容性
console.log('\n📊 测试1: 环境兼容性检查');
const compatibility = {
  https: window.location.protocol === 'https:' || window.location.hostname === 'localhost',
  webCrypto: !!(window.crypto && window.crypto.subtle),
  speechRecognition: 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window,
  mediaDevices: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
  fetch: typeof fetch !== 'undefined'
};

console.table(compatibility);

// 测试2: 测试HMAC签名备用方案
console.log('\n🔐 测试2: HMAC签名备用方案');
async function testHmacFallback() {
  try {
    const response = await fetch('/api/crypto/hmac-sha1', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        content: 'test-content',
        key: 'test-key'
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ 备用签名服务正常工作:', result.signature);
      return true;
    } else {
      console.log('❌ 备用签名服务响应错误:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ 备用签名服务连接失败:', error.message);
    return false;
  }
}

// 测试3: 测试语音识别权限
console.log('\n🎤 测试3: 语音识别功能');
async function testSpeechRecognition() {
  if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
    console.log('❌ 浏览器不支持语音识别');
    return false;
  }

  try {
    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    console.log('✅ 麦克风权限获取成功');
    
    // 立即停止流
    stream.getTracks().forEach(track => track.stop());
    
    // 测试语音识别初始化
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    recognition.lang = 'zh-CN';
    console.log('✅ 语音识别初始化成功');
    
    return true;
  } catch (error) {
    console.log('❌ 语音识别测试失败:', error.message);
    return false;
  }
}

// 测试4: 测试LiblibService
console.log('\n🎨 测试4: LiblibService签名生成');
async function testLiblibService() {
  try {
    // 动态导入LiblibService
    const { default: liblibService } = await import('./src/services/liblibService.js');
    
    // 测试签名生成
    const testUri = '/api/test';
    const testKey = 'test-secret-key';
    
    // 模拟签名生成
    const result = await liblibService.hmacSha1Browser(testUri + '&' + Date.now() + '&test', testKey);
    console.log('✅ LiblibService签名生成成功:', result);
    return true;
  } catch (error) {
    console.log('❌ LiblibService测试失败:', error.message);
    return false;
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('\n🚀 开始运行所有测试...');
  
  const results = {
    hmacFallback: await testHmacFallback(),
    speechRecognition: await testSpeechRecognition(),
    liblibService: await testLiblibService()
  };
  
  console.log('\n📋 测试结果汇总:');
  console.table(results);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 测试完成: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！修复成功！');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }
  
  // 提供建议
  console.log('\n💡 建议:');
  if (!compatibility.https) {
    console.log('- 建议使用HTTPS访问以获得最佳体验');
  }
  if (!results.speechRecognition) {
    console.log('- 语音识别功能需要现代浏览器和麦克风权限');
  }
  if (!results.hmacFallback) {
    console.log('- 请确保开发服务器正在运行');
  }
}

// 自动运行测试
runAllTests().catch(error => {
  console.error('❌ 测试运行失败:', error);
});

// 导出测试函数供手动调用
window.testFixes = {
  runAllTests,
  testHmacFallback,
  testSpeechRecognition,
  testLiblibService,
  compatibility
};

console.log('\n📝 提示: 测试函数已添加到 window.testFixes，可手动调用');
console.log('例如: window.testFixes.testHmacFallback()');
