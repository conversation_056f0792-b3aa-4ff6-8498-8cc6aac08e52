<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenAI API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .loading { background-color: #e2e3e5; border-color: #d6d8db; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .api-key-input {
            width: 100%;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 OpenAI API 连接测试</h1>
        <p>这个测试页面将验证您的 OpenAI API 密钥是否有效，以及各项功能是否正常工作。</p>
        
        <div class="test-section">
            <h3>🔑 API 密钥设置</h3>
            <input type="password" id="apiKey" class="api-key-input" placeholder="请输入您的 OpenAI API 密钥">
            <button onclick="saveApiKey()">保存密钥</button>
            <div id="keyStatus"></div>
        </div>

        <div class="test-section">
            <h3>🌐 测试1: API 连接状态</h3>
            <button onclick="testConnection()" id="testConnectionBtn">测试连接</button>
            <div id="connectionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📝 测试2: GPT-4o 文本生成</h3>
            <button onclick="testTextGeneration()" id="testTextBtn">测试文本生成</button>
            <div id="textResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🎨 测试3: DALL-E 3 图像生成</h3>
            <button onclick="testImageGeneration()" id="testImageBtn">测试图像生成</button>
            <div id="imageResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试4: 智能评估分析</h3>
            <button onclick="testAnalysis()" id="testAnalysisBtn">测试评估分析</button>
            <div id="analysisResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 测试总结</h3>
            <div id="summary"></div>
        </div>
    </div>

    <script>
        let apiKey = '';
        const testResults = {
            connection: false,
            textGeneration: false,
            imageGeneration: false,
            analysis: false
        };

        // 保存API密钥
        function saveApiKey() {
            apiKey = document.getElementById('apiKey').value.trim();
            const statusDiv = document.getElementById('keyStatus');
            
            if (apiKey) {
                localStorage.setItem('openai_api_key', apiKey);
                statusDiv.innerHTML = '<div class="success">✅ API密钥已保存</div>';
            } else {
                statusDiv.innerHTML = '<div class="error">❌ 请输入有效的API密钥</div>';
            }
        }

        // 页面加载时尝试从localStorage获取API密钥
        window.onload = function() {
            const savedKey = localStorage.getItem('openai_api_key');
            if (savedKey) {
                apiKey = savedKey;
                document.getElementById('apiKey').value = savedKey;
                document.getElementById('keyStatus').innerHTML = '<div class="success">✅ 已加载保存的API密钥</div>';
            }
        };

        // 通用的API调用函数
        async function callOpenAI(endpoint, data) {
            if (!apiKey) {
                throw new Error('请先设置API密钥');
            }

            const response = await fetch(`https://api.openai.com/v1${endpoint}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API错误 (${response.status}): ${errorData.error?.message || '未知错误'}`);
            }

            return await response.json();
        }

        // 测试API连接
        async function testConnection() {
            const btn = document.getElementById('testConnectionBtn');
            const result = document.getElementById('connectionResult');
            
            btn.disabled = true;
            btn.textContent = '测试中...';
            result.textContent = '正在检查API连接...';
            result.className = 'result loading';

            try {
                const response = await fetch('https://api.openai.com/v1/models', {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    testResults.connection = true;
                    result.className = 'result success';
                    result.textContent = `✅ API连接成功！\n可用模型数量: ${data.data.length}\n\n部分可用模型:\n${data.data.slice(0, 5).map(m => `- ${m.id}`).join('\n')}`;
                } else {
                    const errorData = await response.json();
                    throw new Error(`${response.status}: ${errorData.error?.message || '未知错误'}`);
                }
            } catch (error) {
                testResults.connection = false;
                result.className = 'result error';
                result.textContent = `❌ 连接失败: ${error.message}`;
            }

            btn.disabled = false;
            btn.textContent = '测试连接';
            updateSummary();
        }

        // 测试文本生成
        async function testTextGeneration() {
            const btn = document.getElementById('testTextBtn');
            const result = document.getElementById('textResult');
            
            btn.disabled = true;
            btn.textContent = '生成中...';
            result.textContent = '正在生成文本...';
            result.className = 'result loading';

            try {
                const data = await callOpenAI('/chat/completions', {
                    model: 'gpt-4o',
                    messages: [
                        {
                            role: 'system',
                            content: '你是一位专业的儿童绘本作家，擅长为自闭症儿童创作故事。'
                        },
                        {
                            role: 'user',
                            content: '请为6-8岁自闭症儿童写一个关于友谊的简短故事开头（50字以内）。'
                        }
                    ],
                    max_tokens: 100,
                    temperature: 0.7
                });

                testResults.textGeneration = true;
                result.className = 'result success';
                result.textContent = `✅ 文本生成成功！\n\n生成的故事开头:\n"${data.choices[0].message.content}"\n\n消耗tokens: ${data.usage?.total_tokens || '未知'}`;
            } catch (error) {
                testResults.textGeneration = false;
                result.className = 'result error';
                result.textContent = `❌ 文本生成失败: ${error.message}`;
            }

            btn.disabled = false;
            btn.textContent = '测试文本生成';
            updateSummary();
        }

        // 测试图像生成
        async function testImageGeneration() {
            const btn = document.getElementById('testImageBtn');
            const result = document.getElementById('imageResult');
            
            btn.disabled = true;
            btn.textContent = '生成中...';
            result.textContent = '正在生成图像...（这可能需要10-30秒）';
            result.className = 'result loading';

            try {
                const data = await callOpenAI('/images/generations', {
                    model: 'dall-e-3',
                    prompt: '为自闭症儿童绘本创作一幅插图：一只友好的小熊在森林中微笑，温暖的色彩，简洁的儿童插画风格',
                    n: 1,
                    size: '1024x1024',
                    quality: 'standard'
                });

                testResults.imageGeneration = true;
                result.className = 'result success';
                result.innerHTML = `✅ 图像生成成功！<br><br>生成的图像:<br><img src="${data.data[0].url}" style="max-width: 300px; border-radius: 8px;"><br><br>图像URL: ${data.data[0].url}<br><br>💡 提示: 图像URL有效期为1小时`;
            } catch (error) {
                testResults.imageGeneration = false;
                result.className = 'result error';
                result.textContent = `❌ 图像生成失败: ${error.message}`;
            }

            btn.disabled = false;
            btn.textContent = '测试图像生成';
            updateSummary();
        }

        // 测试评估分析
        async function testAnalysis() {
            const btn = document.getElementById('testAnalysisBtn');
            const result = document.getElementById('analysisResult');
            
            btn.disabled = true;
            btn.textContent = '分析中...';
            result.textContent = '正在进行智能评估分析...';
            result.className = 'result loading';

            try {
                const analysisPrompt = `
分析自闭症儿童在以下交互问题中的回答表现：

问题1："如果你是波波，你会怎么向小兔子打招呼呢？"
回答1："我会说你好，然后告诉她我叫波波，我想和你做朋友"

问题2："波波看到这么多新朋友有点害怕。如果你是波波，你会怎么融入这个新的朋友圈子？"
回答2："我会先和莉莉打招呼，因为我认识她，然后让她介绍其他朋友给我"

请从以下四个维度进行评估（满分5分）：
1. 语言词汇量：评估词汇丰富度、表达多样性
2. 思维逻辑：评估因果关系理解、逻辑推理能力
3. 社会适应：评估社交规则理解、人际互动意识
4. 情感识别：评估情感表达、共情能力

请简要分析并给出评分。
`;

                const data = await callOpenAI('/chat/completions', {
                    model: 'gpt-4o',
                    messages: [
                        {
                            role: 'system',
                            content: '你是一位专业的儿童心理学家，擅长评估自闭症儿童的语言和社交能力。'
                        },
                        {
                            role: 'user',
                            content: analysisPrompt
                        }
                    ],
                    max_tokens: 600,
                    temperature: 0.3
                });

                testResults.analysis = true;
                result.className = 'result success';
                result.textContent = `✅ 智能评估分析成功！\n\n分析结果:\n${data.choices[0].message.content}`;
            } catch (error) {
                testResults.analysis = false;
                result.className = 'result error';
                result.textContent = `❌ 评估分析失败: ${error.message}`;
            }

            btn.disabled = false;
            btn.textContent = '测试评估分析';
            updateSummary();
        }

        // 更新测试总结
        function updateSummary() {
            const summary = document.getElementById('summary');
            const passedTests = Object.values(testResults).filter(Boolean).length;
            const totalTests = Object.keys(testResults).length;
            const percentage = Math.round(passedTests / totalTests * 100);

            let summaryHTML = `
                <h4>测试结果统计</h4>
                <p>通过率: ${passedTests}/${totalTests} (${percentage}%)</p>
                <ul>
                    <li>API连接: ${testResults.connection ? '✅ 通过' : '❌ 失败'}</li>
                    <li>文本生成: ${testResults.textGeneration ? '✅ 通过' : '❌ 失败'}</li>
                    <li>图像生成: ${testResults.imageGeneration ? '✅ 通过' : '❌ 失败'}</li>
                    <li>智能评估: ${testResults.analysis ? '✅ 通过' : '❌ 失败'}</li>
                </ul>
            `;

            if (passedTests === totalTests) {
                summaryHTML += '<div class="success">🎉 所有测试通过！OpenAI API 集成完全正常，您现在可以在应用中使用所有 AI 功能。</div>';
            } else if (passedTests > 0) {
                summaryHTML += '<div class="warning">⚠️ 部分测试通过。请检查失败的测试项目。</div>';
            } else {
                summaryHTML += '<div class="error">❌ 所有测试失败。请检查API密钥是否正确，账户是否有足够额度。</div>';
            }

            summary.innerHTML = summaryHTML;
        }
    </script>
</body>
</html>
