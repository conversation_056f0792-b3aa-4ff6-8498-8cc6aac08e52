# OpenAI API 测试指南

现在您已经设置了 `.env` 文件并填入了 OpenAI API 密钥，我们提供了两种方式来测试 API 是否正常工作：

## 🌐 方式1：独立测试页面

我已经创建了一个独立的测试页面 `test-api-simple.html`，您可以：

1. **直接在浏览器中打开**：
   - 文件路径：`C:\Users\<USER>\Desktop\交互绘本（manus）\test-api-simple.html`
   - 或者在浏览器地址栏输入：`file:///C:/Users/<USER>/Desktop/交互绘本（manus）/test-api-simple.html`

2. **使用步骤**：
   - 在"API 密钥设置"区域输入您的 OpenAI API 密钥
   - 点击"保存密钥"
   - 依次点击各个测试按钮：
     - 🌐 测试API连接
     - 📝 测试文本生成
     - 🎨 测试图像生成
     - 📊 测试评估分析

## 🎯 方式2：应用内测试面板

在主应用中，我添加了一个测试面板：

1. **访问应用**：
   - 确保开发服务器正在运行：`http://localhost:5173`
   - 在应用右下角会看到一个"🧪 API测试"按钮

2. **使用步骤**：
   - 首先在应用中设置 API 密钥（在介绍页面）
   - 点击右下角的"🧪 API测试"按钮
   - 在弹出的测试面板中运行各项测试

## 🧪 测试项目说明

### 1. API连接测试
- **目的**：验证 API 密钥是否有效
- **测试内容**：获取可用模型列表
- **预期结果**：显示可用模型数量

### 2. GPT-4o 文本生成测试
- **目的**：验证文本生成功能
- **测试内容**：生成一个简短的儿童故事开头
- **预期结果**：返回生成的故事文本和消耗的 tokens

### 3. DALL-E 3 图像生成测试
- **目的**：验证图像生成功能
- **测试内容**：生成一幅儿童插画
- **预期结果**：返回生成的图像URL和预览图
- **注意**：图像生成可能需要10-30秒

### 4. 智能评估分析测试
- **目的**：验证AI评估功能
- **测试内容**：分析模拟的儿童回答
- **预期结果**：返回四维度评估结果

## ✅ 成功标准

如果所有测试都通过，您应该看到：
- ✅ API连接成功
- ✅ 文本生成成功
- ✅ 图像生成成功
- ✅ 智能评估成功
- 🎉 通过率：4/4 (100%)

## ❌ 常见问题排查

### 1. API连接失败
**可能原因**：
- API密钥错误或无效
- 网络连接问题
- OpenAI服务暂时不可用

**解决方案**：
- 检查API密钥是否正确复制
- 确认网络可以访问 `api.openai.com`
- 稍后重试

### 2. 文本生成失败
**可能原因**：
- 账户余额不足
- 没有GPT-4o访问权限
- 请求频率过高

**解决方案**：
- 检查OpenAI账户余额
- 确认账户有GPT-4o访问权限
- 等待几分钟后重试

### 3. 图像生成失败
**可能原因**：
- 账户余额不足
- 没有DALL-E 3访问权限
- 请求内容违反政策

**解决方案**：
- 检查OpenAI账户余额
- 确认账户有DALL-E 3访问权限
- 检查生成内容是否符合OpenAI使用政策

### 4. 智能评估失败
**可能原因**：
- 与文本生成相同的问题
- 请求超时

**解决方案**：
- 参考文本生成的解决方案
- 检查网络稳定性

## 💰 费用说明

测试会产生少量费用：
- **文本生成**：约 $0.001-0.003 每次测试
- **图像生成**：约 $0.04 每次测试
- **智能评估**：约 $0.002-0.005 每次测试

总测试费用通常不超过 $0.05。

## 🔄 重新测试

如果某项测试失败，您可以：
1. 检查并修复问题
2. 单独重新运行失败的测试
3. 或运行"所有测试"重新验证

## 📞 获取帮助

如果测试仍然失败，请检查：
1. `.env` 文件中的 API 密钥格式
2. OpenAI 账户状态和余额
3. 网络连接和防火墙设置

测试成功后，您就可以在应用中正常使用所有 AI 功能了！
