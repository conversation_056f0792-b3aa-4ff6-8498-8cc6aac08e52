// LIBLIB API调试脚本
// 用于测试API连接和查看详细的请求/响应信息
// 使用方法：node debugLiblibAPI.js

// 使用 ES6 导入方式（保持官方签名逻辑）
import dotenv from 'dotenv';
import hmacsha1 from 'hmacsha1';
import randomString from 'string-random';

// 加载环境变量
dotenv.config();

// LIBLIB API配置（按照官方示例）
const API_CONFIG = {
  baseUrl: 'https://openapi.liblibai.cloud',
  textToImageEndpoint: '/api/generate/webui/text2img/ultra',
  imageToImageEndpoint: '/api/generate/webui/img2img/ultra',
  queryEndpoint: '/api/generate/webui/status',
  templateUuid: '5d7e67009b344550bc1aa6ccbfa1d7f4', // 文生图模板
  img2imgTemplateUuid: '07e00af4fc464c7ab55ff906f8acf1b7' // 图生图模板
};

// 生成签名（完全按照官方Node.js示例实现）
const urlSignature = (url, secretKey) => {
  if (!url) return;
  const timestamp = Date.now(); // 当前时间戳
  const signatureNonce = randomString(16); // 随机字符串，你可以任意设置，这个没有要求
  // 原文 = URl地址 + "&" + 毫秒时间戳 + "&" + 随机字符串
  const str = `${url}&${timestamp}&${signatureNonce}`;
  const hash = hmacsha1(secretKey, str);
  // 最后一步： encodeBase64URLSafeString(密文)
  // 这一步很重要，生成安全字符串。java、Python 以外的语言，可以参考这个 JS 的处理
  let signature = hash
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=+$/, "");

  console.log('🔐 签名生成过程（按照官方示例）:');
  console.log('   URL:', url);
  console.log('   时间戳:', timestamp);
  console.log('   随机字符串:', signatureNonce);
  console.log('   原文:', str);
  console.log('   生成的签名:', signature);

  return {
    signature,
    timestamp,
    signatureNonce,
  };
};

// 测试API连接
async function testLiblibAPI() {
  console.log('🧪 开始调试LIBLIB AI API连接');

  try {
    // 检查环境变量
    const accessKey = process.env.VITE_LIBLIB_ACCESS_KEY;
    const secretKey = process.env.VITE_LIBLIB_SECRET_KEY;

    console.log('🔍 详细的环境变量检查:');
    console.log('   原始 AccessKey:', `"${accessKey}"`);
    console.log('   原始 SecretKey:', `"${secretKey}"`);
    console.log('   AccessKey 类型:', typeof accessKey);
    console.log('   SecretKey 类型:', typeof secretKey);

    if (!accessKey || !secretKey) {
      throw new Error('请在.env文件中设置LIBLIB API密钥');
    }

    console.log('✅ API密钥配置检查通过');
    console.log('   AccessKey长度:', accessKey.length);
    console.log('   SecretKey长度:', secretKey.length);
    console.log('   AccessKey完整:', accessKey);
    console.log('   SecretKey完整:', secretKey);

    // 准备请求（按照官方示例使用URL参数）
    const uri = API_CONFIG.textToImageEndpoint;
    const { signature, timestamp, signatureNonce } = urlSignature(uri, secretKey);

    // 按照官方示例，将认证参数放在URL中
    const fullUrl = `${API_CONFIG.baseUrl}${uri}?AccessKey=${accessKey}&Signature=${signature}&Timestamp=${timestamp}&SignatureNonce=${signatureNonce}`;

    const headers = {
      'Content-Type': 'application/json'
    };

    const requestBody = {
      templateUuid: API_CONFIG.templateUuid,
      generateParams: {
        prompt: 'a cute brown bear sitting in front of a wooden house in the forest, children illustration style',
        aspectRatio: "square",  // 使用官方预设：square (1:1, 1024*1024)
        imgCount: 1,           // 必填参数：生成图片数量
        steps: 30              // 推荐的采样步数
      }
    };

    console.log('\n📤 发送请求（按照官方示例格式）:');
    console.log('   完整URL:', fullUrl);
    console.log('   请求头:', JSON.stringify(headers, null, 2));
    console.log('   请求体:', JSON.stringify(requestBody, null, 2));

    // 发送请求
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });

    console.log('\n📥 收到响应:');
    console.log('   状态码:', response.status);
    console.log('   状态文本:', response.statusText);
    console.log('   响应头:', Object.fromEntries(response.headers.entries()));

    // 读取响应内容
    const responseText = await response.text();
    console.log('   响应内容:', responseText);

    if (!response.ok) {
      console.log('\n❌ API请求失败');
      try {
        const errorData = JSON.parse(responseText);
        console.log('   错误详情:', JSON.stringify(errorData, null, 2));
      } catch (parseError) {
        console.log('   原始错误内容:', responseText);
      }
      return { success: false, error: `HTTP ${response.status}: ${responseText}` };
    }

    // 解析响应
    let responseData;
    try {
      responseData = JSON.parse(responseText);
      console.log('\n✅ 响应解析成功:');
      console.log('   解析后的数据:', JSON.stringify(responseData, null, 2));
    } catch (parseError) {
      console.log('\n❌ 响应解析失败:', parseError.message);
      return { success: false, error: '响应解析失败' };
    }

    // 检查响应格式和状态码（LIBLIB API 成功时返回 code: 0）
    if (responseData.code === 0 && responseData.data) {
      const taskId = responseData.data.generateUuid || responseData.data.uuid || responseData.data.id;

      if (taskId) {
        console.log('\n🎉 API调用成功!');
        console.log('   任务ID:', taskId);

        // 测试查询接口
        console.log('\n🔍 测试查询接口...');
        await testQueryAPI(accessKey, secretKey, taskId);

        return { success: true, taskId: taskId };
      } else {
        console.log('\n⚠️ 响应中未找到任务ID');
        console.log('   响应数据字段:', Object.keys(responseData.data || {}));
        return { success: false, error: '响应中缺少任务ID' };
      }
    } else {
      console.log('\n❌ API调用失败');
      console.log('   错误代码:', responseData.code);
      console.log('   错误信息:', responseData.msg || responseData.message);
      return { success: false, error: `API错误: ${responseData.msg || responseData.message}` };
    }

  } catch (error) {
    console.error('\n💥 调试过程中发生错误:', error.message);
    console.error('   错误堆栈:', error.stack);
    return { success: false, error: error.message };
  }
}

// 测试查询API（使用正确的POST方法和端点）
async function testQueryAPI(accessKey, secretKey, taskId) {
  try {
    // 使用正确的查询端点
    const uri = API_CONFIG.queryEndpoint;
    const { signature, timestamp, signatureNonce } = urlSignature(uri, secretKey);

    // 构建完整URL（认证参数在URL中）
    const queryUrl = `${API_CONFIG.baseUrl}${uri}?AccessKey=${accessKey}&Signature=${signature}&Timestamp=${timestamp}&SignatureNonce=${signatureNonce}`;

    // 请求体包含任务ID
    const requestBody = {
      generateUuid: taskId
    };

    console.log('   查询URL:', queryUrl);
    console.log('   请求体:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(queryUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('   查询响应状态:', response.status);

    const responseText = await response.text();
    console.log('   查询响应内容:', responseText);

    if (response.ok) {
      try {
        const queryData = JSON.parse(responseText);
        console.log('   查询数据解析:', JSON.stringify(queryData, null, 2));

        // 检查查询结果
        if (queryData.code === 0 && queryData.data) {
          const generateStatus = queryData.data.generateStatus;
          const percentCompleted = queryData.data.percentCompleted;
          const images = queryData.data.images;

          console.log(`   ✅ 查询成功，任务状态: ${generateStatus}, 进度: ${Math.round(percentCompleted * 100)}%`);

          if (generateStatus === 3 && images && images.length > 0) {
            console.log(`   🎉 图片生成完成: ${images[0]}`);
          } else if (generateStatus === 2) {
            console.log('   ⏳ 图片正在生成中...');
          } else if (generateStatus === 4) {
            console.log('   ❌ 图片生成失败');
          } else {
            console.log(`   📊 任务状态: ${generateStatus}`);
          }
        } else {
          console.log(`   ⚠️  查询返回错误: ${queryData.msg || '未知错误'}`);
        }
      } catch (parseError) {
        console.log('   查询响应解析失败:', parseError.message);
      }
    }

  } catch (error) {
    console.error('   查询API测试失败:', error.message);
  }
}

// 测试图生图API
async function testImageToImageAPI() {
  console.log('\n🖼️ 开始测试图生图API');

  try {
    // 检查环境变量
    const accessKey = process.env.VITE_LIBLIB_ACCESS_KEY;
    const secretKey = process.env.VITE_LIBLIB_SECRET_KEY;

    if (!accessKey || !secretKey) {
      throw new Error('请在.env文件中设置LIBLIB API密钥');
    }

    // 使用一个真实可访问的测试图片URL
    const testImageUrl = 'https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=512&h=512&fit=crop';

    // 准备图生图请求
    const uri = API_CONFIG.imageToImageEndpoint;
    const { signature, timestamp, signatureNonce } = urlSignature(uri, secretKey);

    const fullUrl = `${API_CONFIG.baseUrl}${uri}?AccessKey=${accessKey}&Signature=${signature}&Timestamp=${timestamp}&SignatureNonce=${signatureNonce}`;

    const requestBody = {
      templateUuid: API_CONFIG.img2imgTemplateUuid,
      generateParams: {
        prompt: 'a cute brown bear sitting in front of a wooden house in the forest, children illustration style',
        sourceImage: testImageUrl,
        imgCount: 1
      }
    };

    console.log('\n📤 发送图生图请求:');
    console.log('   完整URL:', fullUrl);
    console.log('   请求体:', JSON.stringify(requestBody, null, 2));

    // 发送请求
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('\n📥 收到图生图响应:');
    console.log('   状态码:', response.status);
    console.log('   状态文本:', response.statusText);

    // 读取响应内容
    const responseText = await response.text();
    console.log('   响应内容:', responseText);

    if (!response.ok) {
      console.log('\n❌ 图生图API请求失败');
      try {
        const errorData = JSON.parse(responseText);
        console.log('   错误详情:', JSON.stringify(errorData, null, 2));
      } catch (parseError) {
        console.log('   原始错误内容:', responseText);
      }
      return { success: false, error: `HTTP ${response.status}: ${responseText}` };
    }

    // 解析响应
    let responseData;
    try {
      responseData = JSON.parse(responseText);
      console.log('\n✅ 图生图响应解析成功:');
      console.log('   解析后的数据:', JSON.stringify(responseData, null, 2));
    } catch (parseError) {
      console.log('\n❌ 图生图响应解析失败:', parseError.message);
      return { success: false, error: '响应解析失败' };
    }

    // 检查响应格式和状态码
    if (responseData.code === 0 && responseData.data) {
      const taskId = responseData.data.generateUuid || responseData.data.uuid || responseData.data.id;

      if (taskId) {
        console.log('\n🎉 图生图API调用成功!');
        console.log('   任务ID:', taskId);
        return { success: true, taskId: taskId };
      } else {
        console.log('\n⚠️ 图生图响应中未找到任务ID');
        return { success: false, error: '响应中缺少任务ID' };
      }
    } else {
      console.log('\n❌ 图生图API调用失败');
      console.log('   错误代码:', responseData.code);
      console.log('   错误信息:', responseData.msg || responseData.message);
      return { success: false, error: `API错误: ${responseData.msg || responseData.message}` };
    }

  } catch (error) {
    console.error('\n💥 图生图测试过程中发生错误:', error.message);
    return { success: false, error: error.message };
  }
}

// 运行调试
async function runAllTests() {
  console.log('🧪 开始完整的LIBLIB AI API测试');

  // 测试文生图
  const textToImageResult = await testLiblibAPI();

  // 测试图生图
  const imageToImageResult = await testImageToImageAPI();

  console.log('\n📊 完整测试结果:');
  console.log('文生图测试:', textToImageResult.success ? '✅ 成功' : '❌ 失败');
  console.log('图生图测试:', imageToImageResult.success ? '✅ 成功' : '❌ 失败');

  if (textToImageResult.success && imageToImageResult.success) {
    console.log('\n🎉 所有API测试通过，可以正常使用');
    console.log('💡 现在可以运行完整的插画生成脚本');
    process.exit(0);
  } else {
    console.log('\n❌ 部分或全部API测试失败');
    console.log('🔧 请检查:');
    console.log('   1. API密钥是否正确');
    console.log('   2. 网络连接是否正常');
    console.log('   3. LIBLIB账户是否有足够余额');
    console.log('   4. 模板UUID是否正确');
    process.exit(1);
  }
}

runAllTests().catch(error => {
  console.error('\n💥 测试脚本执行失败:', error);
  process.exit(1);
});
