@echo off
chcp 65001 >nul
echo 🚀 开始部署交互式绘本应用...

REM 检查Node.js环境
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js，请先安装Node.js 16+
    pause
    exit /b 1
)

echo ✅ Node.js版本:
node --version

REM 检查npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到npm
    pause
    exit /b 1
)

echo ✅ npm版本:
npm --version

REM 安装依赖
echo 📦 安装项目依赖...
npm install

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装完成

REM 构建项目
echo 🔨 构建生产版本...
npm run build

if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建完成

REM 检查git
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到git，请先安装git
    pause
    exit /b 1
)

REM 检查是否已经初始化git仓库
if not exist ".git" (
    echo 📝 初始化Git仓库...
    git init
    git add .
    git commit -m "Initial commit: Interactive Storybook App"
    
    echo.
    echo 🔗 请设置远程仓库地址:
    echo git remote add origin https://github.com/yourusername/interactive-storybook.git
    echo git push -u origin main
) else (
    echo ✅ Git仓库已存在
)

REM 部署选项
echo.
echo 🌐 选择部署方式:
echo 1. GitHub Pages (推荐)
echo 2. Vercel
echo 3. Netlify
echo 4. 本地预览

set /p choice=请选择 (1-4): 

if "%choice%"=="1" (
    echo 📚 GitHub Pages部署指南:
    echo 1. 推送代码到GitHub仓库
    echo 2. 在仓库Settings → Pages中启用GitHub Pages
    echo 3. 选择GitHub Actions作为部署源
    echo 4. 等待自动部署完成
    echo.
    echo 🔗 部署后访问: https://yourusername.github.io/interactive-storybook/
) else if "%choice%"=="2" (
    echo ⚡ Vercel部署指南:
    echo 1. 访问 https://vercel.com
    echo 2. 连接GitHub账户
    echo 3. 导入仓库并部署
    echo 4. 获得自动生成的域名
) else if "%choice%"=="3" (
    echo 🌊 Netlify部署指南:
    echo 1. 访问 https://netlify.com
    echo 2. 拖拽dist文件夹或连接GitHub
    echo 3. 自动部署并获得域名
) else if "%choice%"=="4" (
    echo 👀 启动本地预览...
    npm run preview
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo 🎉 部署准备完成！
echo.
echo 📋 下一步:
echo 1. 配置API密钥（LIBLIB AI）
echo 2. 测试所有功能
echo 3. 分享应用链接
echo.
echo 📖 详细指南请查看: deployment-guide.md
echo 🔧 技术支持请查看: DEPLOYMENT-README.md

pause
