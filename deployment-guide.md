# 🚀 GitHub部署指南

## 方案1：GitHub Pages + Cloudflare（推荐）

### 第一步：准备项目

1. **清理项目文件**
```bash
# 删除不需要的文件
rm -rf node_modules
rm -rf dist
rm -rf .env.local
```

2. **创建生产环境配置**
```bash
# 创建 .env.production 文件
echo "VITE_APP_TITLE=交互式绘本" > .env.production
```

3. **更新package.json**
```json
{
  "scripts": {
    "build": "vite build",
    "preview": "vite preview",
    "deploy": "npm run build && gh-pages -d dist"
  },
  "homepage": "https://yourusername.github.io/interactive-storybook"
}
```

### 第二步：配置GitHub

1. **创建GitHub仓库**
   - 访问 https://github.com/new
   - 仓库名：`interactive-storybook`
   - 设为Public（免费用户需要Public才能使用Pages）

2. **上传代码**
```bash
git init
git add .
git commit -m "Initial commit: Interactive Storybook App"
git branch -M main
git remote add origin https://github.com/yourusername/interactive-storybook.git
git push -u origin main
```

3. **配置GitHub Pages**
   - 进入仓库 Settings → Pages
   - Source: Deploy from a branch
   - Branch: gh-pages
   - 保存

### 第三步：自动部署配置

创建 `.github/workflows/deploy.yml`：

```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build
      run: npm run build
      
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

### 第四步：配置Vite for GitHub Pages

更新 `vite.config.ts`：

```typescript
export default defineConfig({
  base: process.env.NODE_ENV === 'production' ? '/interactive-storybook/' : '/',
  // ... 其他配置
})
```

## 方案2：Vercel部署（简单快速）

### 优势
- ✅ 一键部署
- ✅ 自动HTTPS
- ✅ 全球CDN
- ✅ 自动预览

### 步骤
1. 访问 https://vercel.com
2. 连接GitHub账户
3. 选择仓库并部署
4. 自动获得 `https://your-app.vercel.app` 域名

## 方案3：Netlify部署

### 优势
- ✅ 拖拽部署
- ✅ 表单处理
- ✅ 函数支持

### 步骤
1. 访问 https://netlify.com
2. 拖拽dist文件夹或连接GitHub
3. 自动部署

## 功能保证策略

### 1. API密钥安全
```javascript
// 使用环境变量
const LIBLIB_ACCESS_KEY = import.meta.env.VITE_LIBLIB_ACCESS_KEY;
const LIBLIB_SECRET_KEY = import.meta.env.VITE_LIBLIB_SECRET_KEY;
```

### 2. HTTPS保证
- GitHub Pages: 自动HTTPS
- Vercel/Netlify: 自动HTTPS
- 自定义域名: 免费SSL证书

### 3. 功能兼容性
- ✅ Web Crypto API: HTTPS环境下完全可用
- ✅ 语音识别: 现代浏览器支持
- ✅ 图片生成: API调用正常
- ✅ 本地存储: 浏览器原生支持

### 4. 性能优化
```javascript
// 代码分割
const StoryContainer = lazy(() => import('./components/StoryContainer'));

// 图片懒加载
<img loading="lazy" src={imageUrl} alt="插图" />

// 缓存策略
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时
```

## 推荐部署流程

### 立即可行方案（5分钟）
1. 创建GitHub仓库
2. 上传代码
3. 使用Vercel一键部署
4. 获得永久HTTPS链接

### 完整方案（30分钟）
1. GitHub仓库 + Actions自动部署
2. 自定义域名（可选）
3. Cloudflare CDN加速
4. 监控和分析

## 成本分析

### 完全免费方案
- GitHub Pages: 免费
- Cloudflare: 免费套餐
- 域名: 可选（约$10/年）

### 付费增强方案
- Vercel Pro: $20/月（更多功能）
- 自定义域名: $10-15/年
- 高级监控: 可选

## 部署后验证

### 功能检查清单
- [ ] HTTPS访问正常
- [ ] 语音识别工作
- [ ] 图片生成功能
- [ ] 响应式设计
- [ ] 跨浏览器兼容
- [ ] 移动端适配

### 性能检查
- [ ] 首屏加载 < 3秒
- [ ] 图片优化
- [ ] 代码压缩
- [ ] CDN加速

## 维护建议

### 定期更新
- 依赖包安全更新
- 浏览器兼容性测试
- API接口稳定性检查

### 监控设置
- 错误日志收集
- 性能监控
- 用户行为分析

## 下一步行动

1. **立即执行**：选择部署方案并创建GitHub仓库
2. **配置部署**：设置自动化部署流程
3. **测试验证**：确保所有功能正常
4. **优化完善**：性能优化和用户体验提升
