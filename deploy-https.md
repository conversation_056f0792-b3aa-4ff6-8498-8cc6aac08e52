# HTTPS部署解决方案

## 问题说明

当前应用在 `http://*************:5174/` 访问时出现以下问题：
1. Web Crypto API不可用（需要HTTPS环境）
2. 语音识别权限受限（浏览器对HTTP环境的麦克风权限更严格）

## 解决方案

### 方案1：使用Nginx反向代理 + SSL证书（推荐）

1. **安装Nginx**
```bash
sudo apt update
sudo apt install nginx
```

2. **获取SSL证书（使用Let's Encrypt）**
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

3. **配置Nginx**
创建 `/etc/nginx/sites-available/storybook` 文件：
```nginx
server {
    listen 80;
    server_name *************;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name *************;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    location / {
        proxy_pass http://localhost:5174;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

4. **启用配置**
```bash
sudo ln -s /etc/nginx/sites-available/storybook /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 方案2：使用Cloudflare Tunnel（简单快速）

1. **安装cloudflared**
```bash
wget https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
sudo dpkg -i cloudflared-linux-amd64.deb
```

2. **创建隧道**
```bash
cloudflared tunnel login
cloudflared tunnel create storybook
cloudflared tunnel route dns storybook yourdomain.com
```

3. **配置隧道**
创建 `~/.cloudflared/config.yml`：
```yaml
tunnel: storybook
credentials-file: /home/<USER>/.cloudflared/your-tunnel-id.json

ingress:
  - hostname: yourdomain.com
    service: http://localhost:5174
  - service: http_status:404
```

4. **运行隧道**
```bash
cloudflared tunnel run storybook
```

### 方案3：使用自签名证书（开发测试）

1. **生成自签名证书**
```bash
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes
```

2. **修改Vite配置**
```typescript
export default defineConfig({
  server: {
    https: {
      key: fs.readFileSync('key.pem'),
      cert: fs.readFileSync('cert.pem'),
    },
    host: '0.0.0.0',
    port: 5174
  }
})
```

### 方案4：使用ngrok（临时测试）

1. **安装ngrok**
```bash
npm install -g ngrok
```

2. **启动隧道**
```bash
ngrok http 5174
```

这将提供一个HTTPS URL，如：`https://abc123.ngrok.io`

## 当前的备用方案

我已经在代码中实现了以下备用方案：

1. **服务器端签名代理**：在Vite开发服务器中添加了HMAC签名服务
2. **简化签名方案**：作为最后的备用方案（仅用于测试）
3. **改进的错误处理**：提供更友好的错误信息和建议

## 推荐步骤

1. **立即解决**：重启开发服务器以启用新的备用签名方案
2. **短期解决**：使用ngrok或Cloudflare Tunnel获得HTTPS访问
3. **长期解决**：配置正式的SSL证书和域名

## 测试步骤

1. 重启开发服务器：`npm run dev`
2. 访问 `http://*************:5174/`
3. 检查控制台日志，确认备用签名方案正常工作
4. 测试语音输入和图片生成功能
