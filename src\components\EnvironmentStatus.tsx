import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { generateDiagnosticReport, displayDiagnosticInfo } from '@/utils/environmentDiagnostics';

interface EnvironmentStatusProps {
  onDismiss?: () => void;
}

export function EnvironmentStatus({ onDismiss }: EnvironmentStatusProps) {
  const [report, setReport] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const checkEnvironment = async () => {
      try {
        const diagnosticReport = await generateDiagnosticReport();
        setReport(diagnosticReport);
      } catch (error) {
        console.error('环境检测失败:', error);
      } finally {
        setLoading(false);
      }
    };

    checkEnvironment();
  }, []);

  if (loading) {
    return (
      <Alert>
        <AlertDescription>
          🔍 正在检测环境兼容性...
        </AlertDescription>
      </Alert>
    );
  }

  if (!report) {
    return null;
  }

  const hasIssues = report.recommendations.length > 0;
  const isHttps = report.browser.https;
  const webCryptoAvailable = report.crypto.available && report.crypto.canSign;
  const speechAvailable = report.speech.recognition;

  // 如果所有功能都正常，不显示警告
  if (!hasIssues) {
    return null;
  }

  return (
    <Card className="mb-4 border-orange-200 bg-orange-50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            ⚠️ 环境兼容性提醒
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? '隐藏详情' : '查看详情'}
            </Button>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
              >
                ✕
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* 功能状态概览 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="flex items-center gap-2">
            <Badge variant={isHttps ? "default" : "destructive"}>
              {isHttps ? "✅" : "❌"} HTTPS
            </Badge>
            <span className="text-sm text-gray-600">
              {isHttps ? "安全连接" : "非安全连接"}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant={webCryptoAvailable ? "default" : "secondary"}>
              {webCryptoAvailable ? "✅" : "🔄"} 图片生成
            </Badge>
            <span className="text-sm text-gray-600">
              {webCryptoAvailable ? "正常" : "备用方案"}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant={speechAvailable ? "default" : "destructive"}>
              {speechAvailable ? "✅" : "❌"} 语音识别
            </Badge>
            <span className="text-sm text-gray-600">
              {speechAvailable ? "支持" : "不支持"}
            </span>
          </div>
        </div>

        {/* 主要问题提示 */}
        {!isHttps && (
          <Alert className="mb-3">
            <AlertDescription>
              <strong>🔒 建议使用HTTPS访问</strong><br />
              当前为HTTP环境，某些功能可能受限。建议使用HTTPS访问以获得最佳体验。
            </AlertDescription>
          </Alert>
        )}

        {!webCryptoAvailable && (
          <Alert className="mb-3">
            <AlertDescription>
              <strong>🎨 图片生成使用备用方案</strong><br />
              Web Crypto API不可用，已启用服务器端签名备用方案。功能正常，但可能略慢。
            </AlertDescription>
          </Alert>
        )}

        {!speechAvailable && (
          <Alert className="mb-3">
            <AlertDescription>
              <strong>🎤 语音识别不可用</strong><br />
              建议使用Chrome、Edge或Safari浏览器以获得语音输入功能。
            </AlertDescription>
          </Alert>
        )}

        {/* 详细信息 */}
        {showDetails && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">详细诊断信息</h4>
            <pre className="text-xs text-gray-600 whitespace-pre-wrap">
              {displayDiagnosticInfo(report)}
            </pre>
            
            {/* 解决方案链接 */}
            <div className="mt-3 pt-3 border-t border-gray-200">
              <p className="text-sm text-gray-600 mb-2">
                <strong>解决方案：</strong>
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                {!isHttps && (
                  <li>• 配置HTTPS证书或使用Cloudflare等CDN服务</li>
                )}
                {!speechAvailable && (
                  <li>• 使用支持Web Speech API的现代浏览器</li>
                )}
                <li>• 查看项目根目录的 deploy-https.md 文件获取详细部署指南</li>
              </ul>
            </div>
          </div>
        )}

        {/* 当前环境信息 */}
        <div className="mt-3 pt-3 border-t border-orange-200">
          <div className="flex flex-wrap gap-2 text-xs text-gray-500">
            <span>浏览器: {report.browser.userAgent.split(' ')[0]}</span>
            <span>协议: {window.location.protocol}</span>
            <span>主机: {window.location.hostname}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
