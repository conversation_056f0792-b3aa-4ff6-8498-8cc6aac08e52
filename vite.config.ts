import path from "path"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vite"
import crypto from "crypto"

export default defineConfig({
  plugins: [
    react(),
    // 自定义插件：HMAC签名服务
    {
      name: 'hmac-signature-service',
      configureServer(server) {
        server.middlewares.use('/api/crypto/hmac-sha1', (req, res, next) => {
          if (req.method === 'POST') {
            let body = '';
            req.on('data', chunk => {
              body += chunk.toString();
            });
            req.on('end', () => {
              try {
                const { content, key } = JSON.parse(body);

                // 使用Node.js的crypto模块生成HMAC-SHA1签名
                const hmac = crypto.createHmac('sha1', key);
                hmac.update(content);
                const signature = hmac.digest('base64')
                  .replace(/\+/g, "-")
                  .replace(/\//g, "_")
                  .replace(/=+$/, "");

                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Access-Control-Allow-Origin', '*');
                res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
                res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

                res.end(JSON.stringify({ signature }));
              } catch (error) {
                res.statusCode = 400;
                res.end(JSON.stringify({ error: error.message }));
              }
            });
          } else if (req.method === 'OPTIONS') {
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
            res.statusCode = 200;
            res.end();
          } else {
            next();
          }
        });
      }
    }
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    allowedHosts: [
      "5174-ivezwj4mi8cfqj5srehln-fa821dfd.manusvm.computer",
      "localhost",
      ".manusvm.computer"
    ],
    proxy: {
      // 代理LiblibAI API请求以解决CORS问题
      '/api/liblib': {
        target: 'https://openapi.liblibai.cloud',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/liblib/, ''),
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('代理错误:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('代理请求:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('代理响应:', proxyRes.statusCode, req.url);
          });
        }
      }
    },
    // 确保静态资源可以被外部访问
    host: '0.0.0.0',
    // 允许外部访问静态资源
    cors: true
  }
})
